'use client';

import { useState, useEffect, useCallback } from 'react';
import { Activity, RefreshCw, Heart, Cpu, Database, Shield, Server, Clock, BarChart3, Play, Pause, Settings } from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BackButton } from '@/components/ui/back-button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { actuatorService } from '@/lib/api';
import { ActuatorOverview } from '@/components/dashboard/admin/actuator/overview';
import { ActuatorHealth } from '@/components/dashboard/admin/actuator/health';
import { ActuatorMetrics } from '@/components/dashboard/admin/actuator/metrics';
import { ActuatorEnvironment } from '@/components/dashboard/admin/actuator/environment';
import { ActuatorThreads } from '@/components/dashboard/admin/actuator/threads';
import { ActuatorMappings } from '@/components/dashboard/admin/actuator/mappings';

export default function ActuatorPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5); // seconds
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const loadHealthStatus = async () => {
    try {
      setError(null);
      const health = await actuatorService.getHealth();
      setHealthStatus(health);
    } catch (error) {
      console.error('Health status load error:', error);
      setError(error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu');
      toast.error('Sistem durumu yüklenirken hata oluştu');
    }
  };

  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      await loadHealthStatus();
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Data load error:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const id = setInterval(() => {
        loadData();
      }, refreshInterval * 1000);
      setIntervalId(id);
      return () => {
        if (id) clearInterval(id);
      };
    } else {
      if (intervalId) {
        clearInterval(intervalId);
        setIntervalId(null);
      }
    }
  }, [autoRefresh, refreshInterval, loadData]);

  // Initial load
  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleRefresh = () => {
    loadData();
    toast.success('Veriler yenilendi');
  };

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
    if (!autoRefresh) {
      toast.success(`Otomatik yenileme açıldı (${refreshInterval}s)`);
    } else {
      toast.info('Otomatik yenileme kapatıldı');
    }
  };

  const handleIntervalChange = (value: string) => {
    const newInterval = parseInt(value);
    setRefreshInterval(newInterval);
    toast.success(`Yenileme aralığı ${newInterval} saniye olarak ayarlandı`);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'bg-green-500';
      case 'DOWN':
        return 'bg-red-500';
      case 'OUT_OF_SERVICE':
        return 'bg-yellow-500';
      case 'UNKNOWN':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'UP':
        return 'Çalışıyor';
      case 'DOWN':
        return 'Çalışmıyor';
      case 'OUT_OF_SERVICE':
        return 'Hizmet Dışı';
      case 'UNKNOWN':
        return 'Bilinmiyor';
      default:
        return status || 'Bilinmiyor';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center gap-3">
            <BackButton />
            <Activity className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Sistem İzleme</h1>
              <p className="text-muted-foreground">
                Spring Actuator ile sistem durumu ve performans metrikleri
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            {healthStatus && (
              <div className="flex items-center gap-2">
                <div className={`h-3 w-3 rounded-full ${getStatusColor(healthStatus.status)}`} />
                <span className="text-sm font-medium">
                  {getStatusText(healthStatus.status)}
                </span>
              </div>
            )}

            {/* Auto-refresh controls */}
            <div className="flex items-center gap-2">
              <Select value={refreshInterval.toString()} onValueChange={handleIntervalChange}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5s</SelectItem>
                  <SelectItem value="10">10s</SelectItem>
                  <SelectItem value="30">30s</SelectItem>
                  <SelectItem value="60">60s</SelectItem>
                </SelectContent>
              </Select>

              <Button
                onClick={toggleAutoRefresh}
                variant={autoRefresh ? "default" : "outline"}
                size="sm"
              >
                {autoRefresh ? (
                  <Pause className="h-4 w-4 mr-2" />
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                {autoRefresh ? 'Durdur' : 'Başlat'}
              </Button>
            </div>

            <Button onClick={handleRefresh} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
          </div>
        </div>

        {/* Last Refresh Info */}
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Son güncelleme: {lastRefresh.toLocaleString('tr-TR')} - Şu an: {currentTime.toLocaleString('tr-TR')}</span>
              <div className="flex items-center gap-2">
                <span>Otomatik yenileme: </span>
                {autoRefresh ? (
                  <Badge variant="default" className="text-xs">
                    Açık ({refreshInterval}s)
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="text-xs">
                    Kapalı
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-600">
                <Shield className="h-4 w-4" />
                <span className="text-sm font-medium">Hata: {error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span className="hidden sm:inline">Genel Bakış</span>
            </TabsTrigger>
            <TabsTrigger value="health" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              <span className="hidden sm:inline">Sağlık</span>
            </TabsTrigger>
            <TabsTrigger value="metrics" className="flex items-center gap-2">
              <Cpu className="h-4 w-4" />
              <span className="hidden sm:inline">Metrikler</span>
            </TabsTrigger>
            <TabsTrigger value="environment" className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span className="hidden sm:inline">Ortam</span>
            </TabsTrigger>
            <TabsTrigger value="threads" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span className="hidden sm:inline">Thread'ler</span>
            </TabsTrigger>
            <TabsTrigger value="mappings" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="hidden sm:inline">Mapping'ler</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <ActuatorOverview
              healthStatus={healthStatus}
              isLoading={loading}
              error={error}
              onRefresh={handleRefresh}
              autoRefresh={autoRefresh}
              refreshInterval={refreshInterval}
            />
          </TabsContent>

          <TabsContent value="health" className="space-y-6">
            <ActuatorHealth 
              healthStatus={healthStatus}
              isLoading={loading}
              error={error}
              onRefresh={loadHealthStatus}
            />
          </TabsContent>

          <TabsContent value="metrics" className="space-y-6">
            <ActuatorMetrics
              autoRefresh={autoRefresh}
              refreshInterval={refreshInterval}
            />
          </TabsContent>

          <TabsContent value="environment" className="space-y-6">
            <ActuatorEnvironment />
          </TabsContent>

          <TabsContent value="threads" className="space-y-6">
            <ActuatorThreads />
          </TabsContent>

          <TabsContent value="mappings" className="space-y-6">
            <ActuatorMappings />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
